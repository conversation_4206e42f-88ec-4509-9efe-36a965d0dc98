/**
 * Consolidated Cache Service
 * 
 * Unifies all caching functionality into a single, comprehensive service
 * that eliminates code duplication across multiple cache implementations.
 * 
 * Features:
 * - Redis with memory fallback
 * - Request deduplication
 * - Memoization
 * - Cache invalidation by tags
 * - Performance monitoring
 * - Automatic cleanup
 */

import Redis from 'ioredis';
import crypto from 'crypto';

// Unified cache configuration
export interface CacheConfig {
  // Redis configuration
  redisUrl?: string;
  enableRedis?: boolean;
  redisTimeout?: number;
  maxRetries?: number;
  
  // Memory cache configuration
  maxMemorySize?: number;
  defaultTTL?: number;
  cleanupInterval?: number;
  
  // Deduplication configuration
  enableDeduplication?: boolean;
  deduplicationWindow?: number;
  maxPendingRequests?: number;
  
  // Performance configuration
  enableMetrics?: boolean;
  enableCompression?: boolean;
  compressionThreshold?: number;
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  tags: string[];
  compressed?: boolean;
}

export interface CacheOptions {
  ttl?: number;
  tags?: string[];
  enableDeduplication?: boolean;
  enableCompression?: boolean;
  priority?: number;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  hitRate: number;
  memoryUsage: number;
  redisConnected: boolean;
  totalRequests: number;
  deduplicatedRequests: number;
  averageResponseTime: number;
  keyCount: number;
}

export interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
  requestId: string;
  priority: number;
}

/**
 * Consolidated Cache Service
 * 
 * Single service that handles all caching needs across the application
 */
export class ConsolidatedCacheService {
  private config: Required<CacheConfig>;
  private redis: Redis | null = null;
  private isRedisConnected = false;
  private memoryCache = new Map<string, CacheEntry<any>>();
  private pendingRequests = new Map<string, PendingRequest<any>>();
  private cleanupInterval: NodeJS.Timeout | null = null;
  private metrics: CacheMetrics;

  constructor(config: CacheConfig = {}) {
    this.config = {
      redisUrl: process.env.REDIS_URL || '',
      enableRedis: process.env.NODE_ENV === 'production' && process.env.ENABLE_REDIS === 'true',
      redisTimeout: 5000,
      maxRetries: 3,
      maxMemorySize: 1000,
      defaultTTL: 3600000, // 1 hour in milliseconds
      cleanupInterval: 300000, // 5 minutes
      enableDeduplication: true,
      deduplicationWindow: 30000, // 30 seconds
      maxPendingRequests: 100,
      enableMetrics: true,
      enableCompression: true,
      compressionThreshold: 1024,
      ...config
    };

    this.metrics = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      memoryUsage: 0,
      redisConnected: false,
      totalRequests: 0,
      deduplicatedRequests: 0,
      averageResponseTime: 0,
      keyCount: 0
    };

    this.initializeRedis();
    this.startCleanupProcess();
    this.setupGracefulShutdown();
  }

  /**
   * Initialize the cache service (public method for compatibility)
   */
  async initialize(): Promise<void> {
    await this.initializeRedis();
  }

  /**
   * Initialize Redis connection
   */
  private async initializeRedis(): Promise<void> {
    if (!this.config.enableRedis || !this.config.redisUrl || this.config.redisUrl === 'redis://localhost:6379') {
      // Only show warning in production or when explicitly enabled
      if (process.env.NODE_ENV === 'production' || process.env.ENABLE_REDIS === 'true') {
        console.log('[ConsolidatedCache] Redis disabled, using memory cache only');
      }
      return;
    }

    try {
      this.redis = new Redis(this.config.redisUrl, {
        connectTimeout: this.config.redisTimeout,
        lazyConnect: true,
        maxRetriesPerRequest: this.config.maxRetries,
        enableOfflineQueue: false,
      });

      this.redis.on('connect', () => {
        this.isRedisConnected = true;
        this.metrics.redisConnected = true;
        console.log('[ConsolidatedCache] Redis connected successfully');
      });

      this.redis.on('error', (error) => {
        this.isRedisConnected = false;
        this.metrics.redisConnected = false;
        console.warn('[ConsolidatedCache] Redis error, falling back to memory:', error.message);
      });

      this.redis.on('close', () => {
        this.isRedisConnected = false;
        this.metrics.redisConnected = false;
        console.log('[ConsolidatedCache] Redis connection closed');
      });

      await this.redis.connect();
    } catch (error) {
      console.warn('[ConsolidatedCache] Failed to initialize Redis:', error);
      this.redis = null;
      this.isRedisConnected = false;
    }
  }

  /**
   * Get value from cache with automatic fallback
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Try Redis first if available
      if (this.isRedisConnected && this.redis) {
        const value = await this.redis.get(key);
        if (value) {
          const parsed = this.deserializeValue<T>(value);
          if (parsed !== null) {
            this.metrics.hits++;
            this.updateMetrics(startTime);
            return parsed;
          }
        }
      }

      // Fallback to memory cache
      const memoryEntry = this.memoryCache.get(key);
      if (memoryEntry && !this.isExpired(memoryEntry)) {
        memoryEntry.accessCount++;
        memoryEntry.lastAccessed = Date.now();
        this.metrics.hits++;
        this.updateMetrics(startTime);
        return this.decompressValue<T>(memoryEntry.data, memoryEntry.compressed);
      }

      // Cache miss
      this.metrics.misses++;
      this.updateMetrics(startTime);
      return null;
    } catch (error) {
      console.warn('[ConsolidatedCache] Get operation failed:', error);
      this.metrics.misses++;
      this.updateMetrics(startTime);
      return null;
    }
  }

  /**
   * Set value in cache with automatic distribution
   */
  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    const {
      ttl = this.config.defaultTTL,
      tags = [],
      enableCompression = this.config.enableCompression
    } = options;

    try {
      const serializedValue = this.serializeValue(value, enableCompression);
      const ttlSeconds = Math.floor(ttl / 1000);

      // Store in Redis if available
      if (this.isRedisConnected && this.redis) {
        if (ttlSeconds > 0) {
          await this.redis.setex(key, ttlSeconds, serializedValue);
        } else {
          await this.redis.set(key, serializedValue);
        }
      }

      // Store in memory cache
      const compressed = enableCompression && this.shouldCompress(value);
      const cacheEntry: CacheEntry<T> = {
        data: compressed ? this.compressValue(value) : value,
        timestamp: Date.now(),
        ttl,
        accessCount: 0,
        lastAccessed: Date.now(),
        tags,
        compressed
      };

      // Ensure memory cache doesn't exceed size limit
      if (this.memoryCache.size >= this.config.maxMemorySize) {
        this.evictLeastRecentlyUsed();
      }

      this.memoryCache.set(key, cacheEntry);
    } catch (error) {
      console.warn('[ConsolidatedCache] Set operation failed:', error);
    }
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<boolean> {
    let deleted = false;

    try {
      // Delete from Redis
      if (this.isRedisConnected && this.redis) {
        const result = await this.redis.del(key);
        deleted = result > 0;
      }

      // Delete from memory cache
      const memoryDeleted = this.memoryCache.delete(key);
      return deleted || memoryDeleted;
    } catch (error) {
      console.warn('[ConsolidatedCache] Delete operation failed:', error);
      return this.memoryCache.delete(key);
    }
  }

  /**
   * Clear all cache entries
   */
  async clear(): Promise<void> {
    try {
      if (this.isRedisConnected && this.redis) {
        await this.redis.flushdb();
      }
      this.memoryCache.clear();
    } catch (error) {
      console.warn('[ConsolidatedCache] Clear operation failed:', error);
      this.memoryCache.clear();
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      // Check Redis first if available
      if (this.isRedisConnected && this.redis) {
        const result = await this.redis.exists(key);
        return result > 0;
      }

      // Check memory cache
      return this.memoryCache.has(key);
    } catch (error) {
      console.warn('[ConsolidatedCache] Error checking key existence:', error);
      return this.memoryCache.has(key);
    }
  }

  /**
   * Get TTL (time to live) for a key
   */
  async getTTL(key: string): Promise<number> {
    try {
      // Check Redis first if available
      if (this.isRedisConnected && this.redis) {
        return await this.redis.ttl(key);
      }

      // Check memory cache
      const entry = this.memoryCache.get(key);
      if (entry && entry.ttl > 0) {
        const expiresAt = entry.timestamp + (entry.ttl * 1000);
        const ttl = Math.floor((expiresAt - Date.now()) / 1000);
        return ttl > 0 ? ttl : -1;
      }

      return -1; // Key doesn't exist or no expiration
    } catch (error) {
      console.warn('[ConsolidatedCache] Error getting TTL:', error);
      return -1;
    }
  }

  /**
   * Request deduplication wrapper
   */
  async deduplicate<T>(
    key: string,
    requestFunction: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    if (!this.config.enableDeduplication) {
      return requestFunction();
    }

    // Check if request is already pending
    const pending = this.pendingRequests.get(key);
    if (pending && (Date.now() - pending.timestamp) < this.config.deduplicationWindow) {
      this.metrics.deduplicatedRequests++;
      return pending.promise;
    }

    // Check cache first
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Create new request
    const requestId = this.generateRequestId();
    const promise = requestFunction().then(async (result) => {
      // Cache the result
      await this.set(key, result, options);
      // Remove from pending requests
      this.pendingRequests.delete(key);
      return result;
    }).catch((error) => {
      // Remove from pending requests on error
      this.pendingRequests.delete(key);
      throw error;
    });

    // Store pending request
    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now(),
      requestId,
      priority: options.priority || 1
    });

    // Clean up old pending requests
    this.cleanupPendingRequests();

    return promise;
  }

  /**
   * Invalidate cache entries by tags
   */
  async invalidateByTags(tags: string[]): Promise<number> {
    let invalidated = 0;
    const keysToDelete: string[] = [];

    // Find keys in memory cache with matching tags
    this.memoryCache.forEach((entry, key) => {
      if (entry.tags.some(tag => tags.includes(tag))) {
        keysToDelete.push(key);
      }
    });

    // Delete found keys
    for (const key of keysToDelete) {
      await this.delete(key);
      invalidated++;
    }

    return invalidated;
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    this.metrics.hitRate = this.metrics.totalRequests > 0
      ? this.metrics.hits / this.metrics.totalRequests
      : 0;
    this.metrics.memoryUsage = this.memoryCache.size;
    this.metrics.keyCount = this.memoryCache.size;
    return { ...this.metrics };
  }

  /**
   * Health check for cache service
   */
  async healthCheck(): Promise<{
    redis: boolean;
    memory: boolean;
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics?: any;
  }> {
    try {
      // Test memory cache
      const testKey = '__health_check__';
      const testValue = { timestamp: Date.now() };

      // Test set operation
      await this.set(testKey, testValue, { ttl: 1000 });

      // Test get operation
      const retrieved = await this.get<typeof testValue>(testKey);

      // Test delete operation
      await this.delete(testKey);

      // Check if operations worked
      const memoryHealthy = retrieved !== null && retrieved.timestamp === testValue.timestamp;

      // Test Redis if available
      let redisHealthy = true;
      if (this.isRedisConnected && this.redis) {
        try {
          await this.redis.ping();
        } catch (error) {
          redisHealthy = false;
        }
      } else {
        redisHealthy = false; // Redis not connected
      }

      // Determine overall status
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (memoryHealthy && redisHealthy) {
        status = 'healthy';
      } else if (memoryHealthy) {
        status = 'degraded'; // Memory cache working but Redis not
      } else {
        status = 'unhealthy';
      }

      return {
        redis: redisHealthy,
        memory: memoryHealthy,
        status,
        metrics: this.getMetrics()
      };
    } catch (error) {
      console.warn('[ConsolidatedCache] Health check failed:', error);
      return {
        redis: false,
        memory: false,
        status: 'unhealthy'
      };
    }
  }

  /**
   * Get cache statistics (alias for getMetrics for backward compatibility)
   */
  async getStats(): Promise<CacheMetrics> {
    return this.getMetrics();
  }

  /**
   * AI-specific cache utilities with collision prevention
   */
  generateAIKey(type: string, userId: string, ...params: string[]): string {
    // Add timestamp, random nonce, and hash to prevent collisions
    const timestamp = Date.now();
    const nonce = Math.random().toString(36).substring(2, 8);
    const sanitizedParams = params.map(p => this.sanitizeKeyParam(p));
    const paramHash = this.generateParamHash(sanitizedParams);

    return `ai:${type}:${userId}:${paramHash}:${timestamp}:${nonce}`;
  }

  /**
   * Sanitize parameters to prevent key injection
   */
  private sanitizeKeyParam(param: string): string {
    return param
      .replace(/[^a-zA-Z0-9_-]/g, '_') // Replace special chars with underscore
      .substring(0, 50) // Limit length
      .toLowerCase();
  }

  /**
   * Generate a hash of parameters for collision prevention
   */
  private generateParamHash(params: string[]): string {
    const paramString = params.join('|');
    // Simple hash function for cache keys (not cryptographic)
    let hash = 0;
    for (let i = 0; i < paramString.length; i++) {
      const char = paramString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Cache AI response with specific key generation
   */
  async cacheAIResponse(
    type: string,
    userId: string,
    params: string[],
    response: any,
    ttlSeconds: number = 3600
  ): Promise<void> {
    const key = this.generateAIKey(type, userId, ...params);
    await this.set(key, response, { ttl: ttlSeconds * 1000 });
  }

  /**
   * Get AI response from cache
   */
  async getAIResponse<T>(
    type: string,
    userId: string,
    params: string[]
  ): Promise<T | null> {
    const key = this.generateAIKey(type, userId, ...params);
    return this.get<T>(key);
  }

  /**
   * Memoization wrapper for functions
   */
  memoize<T extends (...args: any[]) => any>(
    fn: T,
    keyGenerator?: (...args: Parameters<T>) => string,
    options: CacheOptions = {}
  ): T {
    const generateKey = keyGenerator || ((...args) => `memoized:${fn.name}:${this.hashArgs(args)}`);

    return ((...args: Parameters<T>) => {
      const key = generateKey(...args);
      return this.deduplicate(key, () => Promise.resolve(fn(...args)), options);
    }) as T;
  }

  /**
   * Async memoization wrapper
   */
  memoizeAsync<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    keyGenerator?: (...args: Parameters<T>) => string,
    options: CacheOptions = {}
  ): T {
    const generateKey = keyGenerator || ((...args) => `async-memoized:${fn.name}:${this.hashArgs(args)}`);

    return ((...args: Parameters<T>) => {
      const key = generateKey(...args);
      return this.deduplicate(key, () => fn(...args), options);
    }) as T;
  }

  // Private utility methods
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    this.memoryCache.forEach((entry, key) => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    });

    if (oldestKey) {
      this.memoryCache.delete(oldestKey);
    }
  }

  private shouldCompress(value: any): boolean {
    if (!this.config.enableCompression) return false;
    const serialized = JSON.stringify(value);
    return serialized.length > this.config.compressionThreshold;
  }

  private compressValue(value: any): any {
    // Simple compression simulation - in production, use actual compression
    return { __compressed: true, data: JSON.stringify(value) };
  }

  private decompressValue<T>(value: any, compressed?: boolean): T {
    if (compressed && value.__compressed) {
      return JSON.parse(value.data);
    }
    return value;
  }

  private serializeValue(value: any, enableCompression: boolean): string {
    if (enableCompression && this.shouldCompress(value)) {
      return JSON.stringify(this.compressValue(value));
    }
    return JSON.stringify(value);
  }

  private deserializeValue<T>(value: string): T | null {
    try {
      const parsed = JSON.parse(value);
      return this.decompressValue<T>(parsed, parsed.__compressed);
    } catch {
      return null;
    }
  }

  private hashArgs(args: any[]): string {
    return crypto.createHash('md5').update(JSON.stringify(args)).digest('hex');
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateMetrics(startTime: number): void {
    if (this.config.enableMetrics) {
      const responseTime = Date.now() - startTime;
      this.metrics.averageResponseTime =
        (this.metrics.averageResponseTime + responseTime) / 2;
    }
  }

  private cleanupPendingRequests(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    this.pendingRequests.forEach((request, key) => {
      if (now - request.timestamp > this.config.deduplicationWindow) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.pendingRequests.delete(key));
  }

  /**
   * Generate a general cache key (for compatibility with tests)
   */
  generateKey(type: string, ...params: string[]): string {
    const baseKey = `cache:${type}`;
    if (params.length === 0) return baseKey;

    const paramHash = this.hashArgs(params);
    return `${baseKey}:${paramHash}`;
  }

  /**
   * Invalidate cache entries by pattern (for compatibility with tests)
   */
  async invalidatePattern(pattern: string): Promise<number> {
    let invalidated = 0;
    const keysToDelete: string[] = [];

    // Find keys in memory cache matching pattern
    this.memoryCache.forEach((_, key) => {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    });

    // Delete matching keys
    for (const key of keysToDelete) {
      await this.delete(key);
      invalidated++;
    }

    return invalidated;
  }

  /**
   * Set multiple values in batch (for compatibility with tests)
   */
  async setBatch(entries: Array<{ key: string; value: any; ttl?: number }>): Promise<void> {
    for (const entry of entries) {
      await this.set(entry.key, entry.value, entry.ttl ? { ttl: entry.ttl } : {});
    }
  }

  /**
   * Get multiple values in batch (for compatibility with tests)
   */
  async getBatch<T>(keys: string[]): Promise<Record<string, T | null>> {
    const result: Record<string, T | null> = {};

    for (const key of keys) {
      result[key] = await this.get<T>(key);
    }

    return result;
  }

  /**
   * Warmup cache with predefined data (for compatibility with tests)
   */
  async warmup(data: Array<{ key: string; value: any; ttl?: number }>): Promise<void> {
    await this.setBatch(data);
  }

  /**
   * Check Redis connection status (for compatibility with tests)
   */
  checkRedisConnection(): boolean {
    return this.isRedisConnected;
  }

  /**
   * Cleanup expired entries (for compatibility with tests)
   */
  cleanup(): void {
    // Clean expired memory cache entries
    const expiredKeys: string[] = [];
    this.memoryCache.forEach((entry, key) => {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    });
    expiredKeys.forEach(key => this.memoryCache.delete(key));

    // Clean old pending requests
    this.cleanupPendingRequests();
  }

  /**
   * Disconnect from Redis (for compatibility with tests)
   */
  async disconnect(): Promise<void> {
    if (this.redis && this.isRedisConnected) {
      await this.redis.quit();
      this.isRedisConnected = false;
    }
  }

  private startCleanupProcess(): void {
    this.cleanupInterval = setInterval(() => {
      // Clean expired memory cache entries
      const expiredKeys: string[] = [];
      this.memoryCache.forEach((entry, key) => {
        if (this.isExpired(entry)) {
          expiredKeys.push(key);
        }
      });

      expiredKeys.forEach(key => this.memoryCache.delete(key));

      // Clean old pending requests
      this.cleanupPendingRequests();
    }, this.config.cleanupInterval);
  }

  private setupGracefulShutdown(): void {
    const shutdown = async () => {
      console.log('[ConsolidatedCache] Shutting down gracefully...');

      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
      }

      if (this.redis && this.isRedisConnected) {
        await this.redis.quit();
      }

      console.log('[ConsolidatedCache] Shutdown complete');
    };

    // Check if listeners already exist to prevent MaxListenersExceededWarning
    if (process.listenerCount('SIGINT') < 10) {
      process.on('SIGINT', shutdown);
    }
    if (process.listenerCount('SIGTERM') < 10) {
      process.on('SIGTERM', shutdown);
    }
  }
}

// Export singleton instance
export const consolidatedCache = new ConsolidatedCacheService();
