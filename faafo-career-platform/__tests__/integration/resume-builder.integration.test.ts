/**
 * Resume Builder Integration Tests
 * 
 * End-to-end integration tests for the resume builder functionality
 * including database operations and API interactions.
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { GET, POST } from '@/app/api/resume-builder/route';
import { GET as GetResume, PUT, DELETE } from '@/app/api/resume-builder/[id]/route';
import prisma from '@/lib/prisma';

// Mock session for testing
jest.mock('next-auth/next');
jest.mock('@/lib/errorReporting');
jest.mock('@/lib/logger');
jest.mock('@/lib/errorTracking');
jest.mock('@/lib/csrf', () => ({
  withCSRFProtection: (req: any, handler: any) => handler()
}));
jest.mock('@/lib/rateLimit', () => ({
  withRateLimit: (req: any, config: any, handler: any) => handler()
}));

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;

describe('Resume Builder Integration Tests', () => {
  const mockSession = {
    user: { email: '<EMAIL>' }
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetServerSession.mockResolvedValue(mockSession);
  });

  describe('Complete Resume Workflow', () => {
    it('should handle complete CRUD workflow for a resume', async () => {
      // Mock user exists
      const mockUser = {
        id: 'test-user-1',
        name: 'Test User',
        email: '<EMAIL>',
        emailVerified: null,
        image: null,
        password: 'hashedpassword',
        passwordResetToken: null,
        passwordResetExpires: null,
        failedLoginAttempts: 0,
        lockedUntil: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      jest.spyOn(prisma.user, 'findUnique').mockResolvedValue(mockUser as any);

      // 1. Create a new resume
      const resumeData = {
        title: 'Software Engineer Resume',
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '******-0123',
          location: 'San Francisco, CA'
        },
        summary: 'Experienced software engineer with 5+ years of experience',
        experience: [
          {
            company: 'Tech Corp',
            position: 'Senior Software Engineer',
            startDate: '2020-01',
            endDate: '2023-12',
            description: 'Led development of web applications',
            achievements: ['Improved performance by 40%', 'Mentored 3 junior developers']
          }
        ],
        education: [
          {
            institution: 'University of California',
            degree: 'Bachelor of Science',
            field: 'Computer Science',
            startDate: '2016-09',
            endDate: '2020-05',
            gpa: '3.8'
          }
        ],
        skills: [
          {
            name: 'JavaScript',
            level: 'ADVANCED',
            category: 'Programming Languages'
          },
          {
            name: 'React',
            level: 'ADVANCED',
            category: 'Frameworks & Libraries'
          }
        ],
        template: 'modern',
        isPublic: false
      };

      const mockCreatedResume = {
        id: 'resume-1',
        userId: 'test-user-1',
        title: resumeData.title,
        summary: null,
        personalInfo: resumeData.personalInfo,
        experience: resumeData.experience,
        education: resumeData.education,
        skills: resumeData.skills,
        projects: [],
        certifications: [],
        languages: [],
        template: resumeData.template,
        isActive: true,
        isPublic: resumeData.isPublic,
        lastExported: null,
        exportCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      jest.spyOn(prisma.resume, 'create').mockResolvedValue(mockCreatedResume as any);

      const createRequest = new NextRequest('http://localhost:3000/api/resume-builder', {
        method: 'POST',
        body: JSON.stringify(resumeData),
        headers: { 'Content-Type': 'application/json' }
      });

      const createResponse = await POST(createRequest);
      const createData = await createResponse.json();

      expect(createResponse.status).toBe(201);
      expect(createData.success).toBe(true);
      expect(createData.data.id).toBe('resume-1');
      expect(createData.data.title).toBe('Software Engineer Resume');

      // 2. Retrieve the created resume
      jest.spyOn(prisma.resume, 'findFirst').mockResolvedValue(mockCreatedResume as any);

      const getRequest = new NextRequest('http://localhost:3000/api/resume-builder/resume-1');
      const getResponse = await GetResume(getRequest, { params: Promise.resolve({ id: 'resume-1' }) });
      const getData = await getResponse.json();

      expect(getResponse.status).toBe(200);
      expect(getData.success).toBe(true);
      expect(getData.data.id).toBe('resume-1');
      expect(getData.data.personalInfo.firstName).toBe('John');

      // 3. Update the resume
      const updateData = {
        title: 'Senior Software Engineer Resume',
        summary: 'Highly experienced software engineer with 5+ years of experience in full-stack development'
      };

      const mockUpdatedResume = {
        ...mockCreatedResume,
        ...updateData,
        updatedAt: new Date()
      };

      jest.spyOn(prisma.resume, 'update').mockResolvedValue(mockUpdatedResume as any);

      const updateRequest = new NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' }
      });

      const updateResponse = await PUT(updateRequest, { params: Promise.resolve({ id: 'resume-1' }) });
      const updateResponseData = await updateResponse.json();

      expect(updateResponse.status).toBe(200);
      expect(updateResponseData.success).toBe(true);
      expect(updateResponseData.data.title).toBe('Senior Software Engineer Resume');

      // 4. List all resumes
      const mockResumeList = [
        {
          id: 'resume-1',
          userId: 'test-user-1',
          title: 'Senior Software Engineer Resume',
          summary: 'Highly experienced software engineer with 5+ years of experience in full-stack development',
          personalInfo: mockCreatedResume.personalInfo,
          experience: mockCreatedResume.experience,
          education: mockCreatedResume.education,
          skills: mockCreatedResume.skills,
          projects: [],
          certifications: [],
          languages: [],
          template: 'modern',
          isActive: true,
          isPublic: false,
          lastExported: null,
          exportCount: 0,
          createdAt: mockCreatedResume.createdAt,
          updatedAt: mockUpdatedResume.updatedAt
        }
      ];

      jest.spyOn(prisma.resume, 'findMany').mockResolvedValue(mockResumeList as any);

      const listRequest = new NextRequest('http://localhost:3000/api/resume-builder');
      const listResponse = await GET(listRequest);
      const listData = await listResponse.json();

      expect(listResponse.status).toBe(200);
      expect(listData.success).toBe(true);
      expect(listData.data).toHaveLength(1);
      expect(listData.data[0].title).toBe('Senior Software Engineer Resume');

      // 5. Delete the resume (soft delete)
      const mockDeletedResume = {
        ...mockUpdatedResume,
        isActive: false,
        updatedAt: new Date()
      };

      jest.spyOn(prisma.resume, 'update').mockResolvedValue(mockDeletedResume);

      const deleteRequest = new NextRequest('http://localhost:3000/api/resume-builder/resume-1', {
        method: 'DELETE'
      });

      const deleteResponse = await DELETE(deleteRequest, { params: Promise.resolve({ id: 'resume-1' }) });
      const deleteData = await deleteResponse.json();

      expect(deleteResponse.status).toBe(200);
      expect(deleteData.success).toBe(true);
      expect(deleteData.message).toBe('Resume deleted successfully');

      // Verify the resume was soft deleted
      expect(prisma.resume.update).toHaveBeenCalledWith({
        where: { id: 'resume-1' },
        data: { 
          isActive: false,
          updatedAt: expect.any(Date)
        }
      });
    });

    it('should handle validation errors properly', async () => {
      const mockUser = { id: 'test-user-1' };
      jest.spyOn(prisma.user, 'findUnique').mockResolvedValue(mockUser);

      // Try to create resume with invalid data
      const invalidResumeData = {
        // Missing required title
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          email: 'invalid-email' // Invalid email format
        }
      };

      const createRequest = new NextRequest('http://localhost:3000/api/resume-builder', {
        method: 'POST',
        body: JSON.stringify(invalidResumeData),
        headers: { 'Content-Type': 'application/json' }
      });

      const createResponse = await POST(createRequest);
      const createData = await createResponse.json();

      expect(createResponse.status).toBe(400);
      expect(createData.success).toBe(false);
      expect(createData.error).toBe('Validation failed');
      expect(createData.details).toBeDefined();
    });

    it('should handle unauthorized access properly', async () => {
      // Mock no session
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/resume-builder');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Not authenticated');
    });

    it('should handle user not found scenario', async () => {
      // Mock user not found
      jest.spyOn(prisma.user, 'findUnique').mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/resume-builder');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('User not found');
    });

    it('should handle resume not found scenario', async () => {
      const mockUser = { id: 'test-user-1' };
      jest.spyOn(prisma.user, 'findUnique').mockResolvedValue(mockUser);
      jest.spyOn(prisma.resume, 'findFirst').mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/resume-builder/nonexistent-resume');
      const response = await GetResume(request, { params: Promise.resolve({ id: 'nonexistent-resume' }) });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Resume not found');
    });
  });

  describe('Data Integrity Tests', () => {
    it('should ensure user can only access their own resumes', async () => {
      const mockUser = { id: 'user-1' };
      jest.spyOn(prisma.user, 'findUnique').mockResolvedValue(mockUser);

      // Mock resume belonging to different user
      jest.spyOn(prisma.resume, 'findFirst').mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/resume-builder/other-user-resume');
      const response = await GetResume(request, { params: Promise.resolve({ id: 'other-user-resume' }) });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe('Resume not found');

      // Verify the query included user ownership check
      expect(prisma.resume.findFirst).toHaveBeenCalledWith({
        where: { 
          id: 'other-user-resume',
          userId: 'user-1',
          isActive: true
        }
      });
    });

    it('should only return active resumes in list', async () => {
      const mockUser = { id: 'user-1' };
      jest.spyOn(prisma.user, 'findUnique').mockResolvedValue(mockUser);
      jest.spyOn(prisma.resume, 'findMany').mockResolvedValue([]);

      const request = new NextRequest('http://localhost:3000/api/resume-builder');
      await GET(request);

      expect(prisma.resume.findMany).toHaveBeenCalledWith({
        where: { 
          userId: 'user-1',
          isActive: true
        },
        select: expect.any(Object),
        orderBy: { updatedAt: 'desc' }
      });
    });
  });
});
