/**
 * Comprehensive Tests for Monitoring System
 * Tests logging, metrics, alerting, and monitoring middleware
 */

// Mock the monitoring modules for testing
const mockLogger: any = {
  logSkillSearch: jest.fn(),
  logSkillAssessment: jest.fn(),
  logError: jest.fn(),
  logSecurityEvent: jest.fn(),
  child: jest.fn((): any => mockLogger),
};

const mockMetrics = {
  recordSkillSearch: jest.fn(),
  recordSkillAssessment: jest.fn(),
  recordAIServiceCall: jest.fn(),
  recordError: jest.fn(),
  recordMetric: jest.fn(),
  getMetricStats: jest.fn(),
  exportMetrics: jest.fn(),
  getSystemHealth: jest.fn(() => ({
    status: 'healthy',
    metrics: {},
    alerts: [],
  })),
  clearMetrics: jest.fn(),
  startSystemMetricsCollection: jest.fn(),
  stopSystemMetricsCollection: jest.fn(),
  addAlertRule: jest.fn(),
};

const mockAlerting = {
  createAlert: jest.fn(() => Promise.resolve('alert-123')),
  acknowledgeAlert: jest.fn(() => true),
  resolveAlert: jest.fn(() => true),
  getActiveAlerts: jest.fn(() => []),
  getAlertsBySeverity: jest.fn(() => []),
  getAlertingStats: jest.fn(() => ({
    totalAlerts: 0,
    activeAlerts: 0,
    alertsBySeverity: { low: 0, medium: 0, high: 0, critical: 0 },
    averageResolutionTime: 0,
    acknowledgmentRate: 0,
  })),
  addNotificationChannel: jest.fn(),
  testNotificationChannel: jest.fn(() => Promise.resolve(true)),
  once: jest.fn(),
};

// Mock the classes
jest.mock('@/lib/monitoring/logger', () => ({
  SkillGapLogger: jest.fn(() => mockLogger),
  skillGapLogger: mockLogger,
}));

jest.mock('@/lib/monitoring/metrics', () => ({
  SkillGapMetrics: jest.fn(() => mockMetrics),
  skillGapMetrics: mockMetrics,
}));

jest.mock('@/lib/monitoring/alerting', () => ({
  SkillGapAlerting: jest.fn(() => mockAlerting),
  skillGapAlerting: mockAlerting,
}));

import { NextRequest, NextResponse } from 'next/server';

describe('Monitoring System', () => {
  describe('SkillGapLogger', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should log skill search events', () => {
      const context = {
        query: 'JavaScript',
        resultCount: 5,
        userId: 'user123',
        sessionId: 'session456',
        duration: 150,
        performanceMetrics: {
          responseTime: 150,
          memoryUsage: 45,
          cacheHit: true,
        },
      };

      mockLogger.logSkillSearch(context);
      expect(mockLogger.logSkillSearch).toHaveBeenCalledWith(context);
    });

    it('should log skill assessment events', () => {
      const context = {
        skillCount: 3,
        userId: 'user123',
        sessionId: 'session456',
        duration: 500,
        assessmentData: [
          { skillName: 'JavaScript', selfRating: 8 },
          { skillName: 'React', selfRating: 7 },
          { skillName: 'Node.js', selfRating: 6 },
        ],
      };

      mockLogger.logSkillAssessment(context);
      expect(mockLogger.logSkillAssessment).toHaveBeenCalledWith(context);
    });

    it('should log errors with context', () => {
      const error = new Error('Test error');
      const context = {
        errorCode: 'SKILL_001',
        feature: 'skill_search',
        userId: 'user123',
        sessionId: 'session456',
        metadata: { query: 'invalid' },
      };

      mockLogger.logError(error, context);
      expect(mockLogger.logError).toHaveBeenCalledWith(error, context);
    });

    it('should log security events', () => {
      const context = {
        eventType: 'suspicious_activity',
        severity: 'high' as const,
        details: 'Multiple failed login attempts',
        userId: 'user123',
        sessionId: 'session456',
      };

      mockLogger.logSecurityEvent(context);
      expect(mockLogger.logSecurityEvent).toHaveBeenCalledWith(context);
    });

    it('should create child logger with context', () => {
      const childContext = { userId: 'user123', feature: 'skill_gap' };
      const childLogger = mockLogger.child(childContext);

      expect(mockLogger.child).toHaveBeenCalledWith(childContext);
      expect(childLogger).toBe(mockLogger);
    });
  });

  describe('SkillGapMetrics', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      mockMetrics.getMetricStats.mockReturnValue({
        count: 1,
        min: 150,
        max: 150,
        avg: 150,
        p50: 150,
        p95: 150,
        p99: 150,
      });
    });

    it('should record skill search metrics', () => {
      mockMetrics.recordSkillSearch(150, 5, true);
      expect(mockMetrics.recordSkillSearch).toHaveBeenCalledWith(150, 5, true);

      const searchDurationStats = mockMetrics.getMetricStats('skill_search_duration');
      expect(searchDurationStats).toBeTruthy();
      expect(searchDurationStats?.count).toBe(1);
      expect(searchDurationStats?.avg).toBe(150);
    });

    it('should record skill assessment metrics', () => {
      mockMetrics.recordSkillAssessment(500, 3, 'user123');
      expect(mockMetrics.recordSkillAssessment).toHaveBeenCalledWith(500, 3, 'user123');
    });

    it('should record AI service metrics', () => {
      mockMetrics.recordAIServiceCall('gemini', 2000, 150, true);
      expect(mockMetrics.recordAIServiceCall).toHaveBeenCalledWith('gemini', 2000, 150, true);
    });

    it('should record error metrics', () => {
      mockMetrics.recordError('ValidationError', '/api/skills/search', 400);
      expect(mockMetrics.recordError).toHaveBeenCalledWith('ValidationError', '/api/skills/search', 400);
    });

    it('should get system health status', () => {
      const health = mockMetrics.getSystemHealth();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('metrics');
      expect(health).toHaveProperty('alerts');
      expect(health.status).toBe('healthy');
    });

    it('should start and stop system metrics collection', () => {
      mockMetrics.startSystemMetricsCollection();
      mockMetrics.stopSystemMetricsCollection();

      expect(mockMetrics.startSystemMetricsCollection).toHaveBeenCalled();
      expect(mockMetrics.stopSystemMetricsCollection).toHaveBeenCalled();
    });
  });

  describe('SkillGapAlerting', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should create alerts', async () => {
      const alertId = await mockAlerting.createAlert({
        title: 'Test Alert',
        message: 'This is a test alert',
        severity: 'medium',
        source: 'test',
      });

      expect(alertId).toBe('alert-123');
      expect(mockAlerting.createAlert).toHaveBeenCalled();
    });

    it('should acknowledge alerts', () => {
      const acknowledged = mockAlerting.acknowledgeAlert('alert-123', 'user123');
      expect(acknowledged).toBe(true);
      expect(mockAlerting.acknowledgeAlert).toHaveBeenCalledWith('alert-123', 'user123');
    });

    it('should resolve alerts', () => {
      const resolved = mockAlerting.resolveAlert('alert-123', 'user123');
      expect(resolved).toBe(true);
      expect(mockAlerting.resolveAlert).toHaveBeenCalledWith('alert-123', 'user123');
    });

    it('should get active alerts', () => {
      const activeAlerts = mockAlerting.getActiveAlerts();
      expect(Array.isArray(activeAlerts)).toBe(true);
      expect(mockAlerting.getActiveAlerts).toHaveBeenCalled();
    });

    it('should get alerting statistics', () => {
      const stats = mockAlerting.getAlertingStats();
      expect(stats).toHaveProperty('totalAlerts');
      expect(stats).toHaveProperty('activeAlerts');
      expect(stats).toHaveProperty('acknowledgmentRate');
      expect(mockAlerting.getAlertingStats).toHaveBeenCalled();
    });

    it('should test notification channels', async () => {
      const testResult = await mockAlerting.testNotificationChannel('test-channel');
      expect(testResult).toBe(true);
      expect(mockAlerting.testNotificationChannel).toHaveBeenCalledWith('test-channel');
    });
  });

  describe('Monitoring System Integration', () => {
    it('should provide comprehensive monitoring capabilities', () => {
      // Test that all monitoring components are available
      expect(mockLogger).toBeDefined();
      expect(mockMetrics).toBeDefined();
      expect(mockAlerting).toBeDefined();
    });

    it('should track skill gap operations', () => {
      // Mock the tracking function
      const trackSkillGapOperation = jest.fn();

      trackSkillGapOperation('skill_search', 150, {
        query: 'JavaScript',
        resultCount: 5,
      });

      expect(trackSkillGapOperation).toHaveBeenCalledWith('skill_search', 150, {
        query: 'JavaScript',
        resultCount: 5,
      });
    });

    it('should handle monitoring system initialization', () => {
      // Test that monitoring system can be initialized
      expect(() => {
        mockMetrics.startSystemMetricsCollection();
        mockAlerting.addNotificationChannel({
          id: 'test',
          type: 'console',
          config: {},
          enabled: true,
          severityFilter: ['high', 'critical'],
        });
      }).not.toThrow();
    });
  });
});
