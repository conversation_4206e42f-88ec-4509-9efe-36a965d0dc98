module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  setupFiles: ['<rootDir>/jest.polyfills.js'],

  // Performance Optimizations
  maxWorkers: process.env.CI ? 2 : '25%', // Reduced for better memory management
  workerIdleMemoryLimit: '512MB', // Prevent memory leaks in workers
  cache: true,
  cacheDirectory: '<rootDir>/.jest-cache',
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // Test Execution Optimizations
  testTimeout: 30000, // Reduced from 60s for faster feedback
  slowTestThreshold: 5, // Mark tests >5s as slow
  bail: 0, // Don't bail on first failure for CI

  // Memory Management
  logHeapUsage: true,
  detectOpenHandles: true,
  forceExit: true,

  moduleNameMapper: {
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
    '^@/emails/(.*)$': '<rootDir>/src/emails/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/services/(.*)$': '<rootDir>/src/lib/services/$1',
    '^@/utils/(.*)$': '<rootDir>/src/lib/utils/$1',
    // Specific cache service mapping
    '^@/lib/services/cacheService$': '<rootDir>/src/lib/cache',
    // CSS and static files
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': '<rootDir>/__mocks__/fileMock.js',
  },

  transformIgnorePatterns: [
    'node_modules/(?!(jose|openid-client|next-auth|@panva|oauth4webapi|@radix-ui)/)',
  ],

  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: {
        jsx: 'react-jsx',
      },
    }],
    '^.+\\.(js|jsx)$': ['babel-jest'],
  },

  // Handle ES modules
  extensionsToTreatAsEsm: ['.ts', '.tsx'],

  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // Test Discovery Optimization
  testMatch: [
    '**/__tests__/**/*.test.(ts|tsx|js|jsx)',
    '**/*.test.(ts|tsx|js|jsx)'
  ],

  // Only exclude node_modules for now
  testPathIgnorePatterns: [
    '/node_modules/',
  ],

  // Import coverage configuration
  ...require('./jest.coverage.config.js').getCoverageThreshold ? {
    collectCoverageFrom: require('./jest.coverage.config.js').getCollectCoverageFrom(),
    coverageReporters: require('./jest.coverage.config.js').getCoverageReporters(),
    coverageThreshold: require('./jest.coverage.config.js').getCoverageThreshold(),
  } : {
    // Fallback configuration
    collectCoverageFrom: [
      'src/**/*.{ts,tsx}',
      'components/**/*.{ts,tsx}',
      'lib/**/*.{ts,tsx}',
      'middleware.ts',
      '!src/**/*.d.ts',
      '!**/__tests__/**',
      '!**/node_modules/**',
      '!**/coverage/**',
      '!jest.config.js',
      '!jest.setup.js',
      '!jest.polyfills.js',
    ],
    coverageReporters: ['text-summary', 'lcov'],
    coverageThreshold: {
      global: {
        branches: 70,
        functions: 75,
        lines: 75,
        statements: 75
      }
    }
  },

  verbose: false, // Reduced verbosity for performance
  silent: false,

  // Test result processor for performance monitoring
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: './test-results',
      outputName: 'junit.xml',
    }]
  ],
};
